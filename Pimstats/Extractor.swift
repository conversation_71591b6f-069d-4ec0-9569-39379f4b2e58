import Foundation

struct Match: Decodable {
    struct Team: Decodable {
        let name: String
    }

    struct Score: Decodable {
        struct FullTime: Decodable {
            let home: Int?
            let away: Int?
        }
        let fullTime: FullTime
    }

    let status: String
    let matchday: Int
    let utcDate: String
    let homeTeam: Team
    let awayTeam: Team
    let score: Score
}

typealias TeamStats = [Int: (points: Int, gf: Int, ga: Int)]
typealias LeagueStats = [String: TeamStats]

let token = "0f296f6c02384ad6bb36d034affa7ada"
let season = 2024
let league = "PL"
let baseURL = "https://api.football-data.org/v4"
let headers = ["X-Auth-Token": token]

func fetchMatches() async throws -> [Match] {
    guard let url = URL(string: "\(baseURL)/competitions/\(league)/matches?season=\(season)") else {
        throw URLError(.badURL)
    }

    var request = URLRequest(url: url)
    headers.forEach { key, value in request.setValue(value, forHTTPHeaderField: key) }

    let (data, _) = try await URLSession.shared.data(for: request)
    let decoded = try JSONDecoder().decode([String: [Match]].self, from: data)
    return decoded["matches"] ?? []
}

func processMatches(_ matches: [Match]) -> (LeagueStats, [String], [Int]) {
    var stats: LeagueStats = [:]
    var standings: [String: (points: Int, gf: Int, ga: Int)] = [:]
    var teams: Set<String> = []
    var matchdays: Set<Int> = []

    let finishedMatches = matches
        .filter { $0.status == "FINISHED" }
        .sorted { ($0.matchday, $0.utcDate) < ($1.matchday, $1.utcDate) }

    for match in finishedMatches {
        let md = match.matchday
        let home = match.homeTeam.name
        let away = match.awayTeam.name
        let goalsHome = match.score.fullTime.home ?? 0
        let goalsAway = match.score.fullTime.away ?? 0

        teams.insert(home)
        teams.insert(away)
        matchdays.insert(md)

        // Initialize data if needed
        for team in [home, away] {
            if stats[team] == nil { stats[team] = [:] }
            if standings[team] == nil { standings[team] = (0, 0, 0) }
            stats[team]![md] = standings[team]! // Save snapshot before update
        }

        // Update result
        if goalsHome > goalsAway {
            standings[home]!.points += 3
        } else if goalsHome < goalsAway {
            standings[away]!.points += 3
        } else {
            standings[home]!.points += 1
            standings[away]!.points += 1
        }

        standings[home]!.gf += goalsHome
        standings[home]!.ga += goalsAway
        standings[away]!.gf += goalsAway
        standings[away]!.ga += goalsHome
    }

    return (stats, Array(teams).sorted(), Array(matchdays).sorted())
}

func exportCSV(stats: LeagueStats, teams: [String], matchdays: [Int], to path: String) throws {
    var lines: [String] = []
    let header = ["Team"] + matchdays.map { "MD\($0)" }
    lines.append(header.joined(separator: ","))

    for team in teams {
        var row: [String] = [team]
        for md in matchdays {
            if let stat = stats[team]?[md] {
                row.append("\(stat.points),\(stat.gf),\(stat.ga)")
            } else {
                row.append(",,")
            }
        }
        lines.append(row.joined(separator: ","))
    }

    let csv = lines.joined(separator: "\n")
    try csv.write(to: URL(fileURLWithPath: path), atomically: true, encoding: .utf8)
}

struct FootballStatsCLI {
    static func extract() async {
        do {
            let matches = try await fetchMatches()
            let (stats, teams, matchdays) = processMatches(matches)

            let fileName = "\(league)_\(season)_history.csv"
            let filePath = FileManager.default.currentDirectoryPath + "/" + fileName
            try exportCSV(stats: stats, teams: teams, matchdays: matchdays, to: filePath)

            print("✅ Exported to \(filePath)")
        } catch {
            print("❌ Error: \(error)")
        }
    }
}

// MARK: - Data Manager for SwiftUI Views
@MainActor
class FootballDataManager: ObservableObject {
    @Published var leagueStats: LeagueStats = [:]
    @Published var teams: [String] = []
    @Published var matchdays: [Int] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    func loadData() async {
        isLoading = true
        errorMessage = nil

        do {
            let matches = try await fetchMatches()
            let (stats, teamList, matchdayList) = processMatches(matches)

            self.leagueStats = stats
            self.teams = teamList
            self.matchdays = matchdayList
        } catch {
            self.errorMessage = "Failed to load data: \(error.localizedDescription)"
        }

        isLoading = false
    }

    func getPointsForMatchday(_ matchday: Int) -> [Entry] {
        var entries: [Entry] = []

        for team in teams {
            let points = leagueStats[team]?[matchday]?.points ?? 0
            entries.append(Entry(id: team, value: Double(points)))
        }

        return entries.sorted { $0.value > $1.value }
    }
}
