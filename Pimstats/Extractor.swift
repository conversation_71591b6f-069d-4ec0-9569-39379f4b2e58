import Foundation

// Separate Team structure for teams endpoint
struct TeamDetails: Decodable {
    let id: Int
    let name: String
    let crest: String? // Team logo URL
    let clubColors: String? // Team colors (e.g., "Red / White")
}

struct TeamsResponse: Decodable {
    let teams: [TeamDetails]
}

struct Match: Decodable {
    struct Team: Decodable {
        let id: Int
        let name: String
    }

    struct Score: Decodable {
        struct FullTime: Decodable {
            let home: Int?
            let away: Int?
        }
        let fullTime: FullTime
    }

    let status: String
    let matchday: Int
    let utcDate: String
    let homeTeam: Team
    let awayTeam: Team
    let score: Score
}

typealias TeamStats = [Int: (points: Int, gf: Int, ga: Int)]
typealias LeagueStats = [String: TeamStats]

let token = "0f296f6c02384ad6bb36d034affa7ada"
let season = 2024
let league = "PL"
let baseURL = "https://api.football-data.org/v4"
let headers = ["X-Auth-Token": token]

// Response wrapper for the API
struct MatchesResponse: Decodable {
    let matches: [Match]
}

func fetchTeams() async throws -> [TeamDetails] {
    guard let url = URL(string: "\(baseURL)/competitions/\(league)/teams?season=\(season)") else {
        throw URLError(.badURL)
    }

    var request = URLRequest(url: url)
    headers.forEach { key, value in request.setValue(value, forHTTPHeaderField: key) }

    let (data, response) = try await URLSession.shared.data(for: request)

    // Check HTTP response
    if let httpResponse = response as? HTTPURLResponse {
        print("Teams HTTP Status: \(httpResponse.statusCode)")
        if httpResponse.statusCode != 200 {
            throw URLError(.badServerResponse)
        }
    }

    // Debug: Print raw response
    if let jsonString = String(data: data, encoding: .utf8) {
        print("Teams API Response (first 500 chars): \(String(jsonString.prefix(500)))")
    }

    do {
        let decoded = try JSONDecoder().decode(TeamsResponse.self, from: data)
        print("Successfully decoded \(decoded.teams.count) teams")
        return decoded.teams
    } catch {
        print("Teams decoding error: \(error)")
        throw error
    }
}

func fetchMatches() async throws -> [Match] {
    guard let url = URL(string: "\(baseURL)/competitions/\(league)/matches?season=\(season)") else {
        throw URLError(.badURL)
    }

    var request = URLRequest(url: url)
    headers.forEach { key, value in request.setValue(value, forHTTPHeaderField: key) }

    let (data, response) = try await URLSession.shared.data(for: request)

    // Check HTTP response
    if let httpResponse = response as? HTTPURLResponse {
        print("HTTP Status: \(httpResponse.statusCode)")
        if httpResponse.statusCode != 200 {
            throw URLError(.badServerResponse)
        }
    }

    // Debug: Print raw response
    if let jsonString = String(data: data, encoding: .utf8) {
        print("Raw API Response (first 500 chars): \(String(jsonString.prefix(500)))")
    }

    do {
        let decoded = try JSONDecoder().decode(MatchesResponse.self, from: data)
        print("Successfully decoded \(decoded.matches.count) matches")
        return decoded.matches
    } catch {
        print("Decoding error: \(error)")
        // Try alternative decoding structure
        do {
            let alternativeDecoded = try JSONDecoder().decode([String: [Match]].self, from: data)
            print("Successfully decoded with alternative structure")
            return alternativeDecoded["matches"] ?? []
        } catch {
            print("Alternative decoding also failed: \(error)")
            throw error
        }
    }
}

func processData(_ matches: [Match], _ teams: [TeamDetails]) -> (LeagueStats, [String], [Int], [String: String], [String: String]) {
    var stats: LeagueStats = [:]
    var standings: [String: (points: Int, gf: Int, ga: Int)] = [:]
    var teamNames: Set<String> = []
    var matchdays: Set<Int> = []
    var teamLogos: [String: String] = [:]
    var teamClubColors: [String: String] = [:]

    // First, build team lookup maps from teams data
    var teamIdToName: [Int: String] = [:]
    for team in teams {
        teamIdToName[team.id] = team.name
        teamNames.insert(team.name)
        if let crest = team.crest {
            teamLogos[team.name] = crest
        }
        if let colors = team.clubColors {
            teamClubColors[team.name] = colors
        }
    }

    let finishedMatches = matches
        .filter { $0.status == "FINISHED" }
        .sorted { ($0.matchday, $0.utcDate) < ($1.matchday, $1.utcDate) }

    for match in finishedMatches {
        let md = match.matchday
        // Use team IDs to get names from our lookup
        guard let home = teamIdToName[match.homeTeam.id],
              let away = teamIdToName[match.awayTeam.id] else {
            continue // Skip if team not found
        }
        let goalsHome = match.score.fullTime.home ?? 0
        let goalsAway = match.score.fullTime.away ?? 0

        matchdays.insert(md)

        // Initialize data if needed
        for team in [home, away] {
            if stats[team] == nil { stats[team] = [:] }
            if standings[team] == nil { standings[team] = (0, 0, 0) }
            stats[team]![md] = standings[team]! // Save snapshot before update
        }

        // Update result
        if goalsHome > goalsAway {
            standings[home]!.points += 3
        } else if goalsHome < goalsAway {
            standings[away]!.points += 3
        } else {
            standings[home]!.points += 1
            standings[away]!.points += 1
        }

        standings[home]!.gf += goalsHome
        standings[home]!.ga += goalsAway
        standings[away]!.gf += goalsAway
        standings[away]!.ga += goalsHome
    }

    return (stats, Array(teamNames).sorted(), Array(matchdays).sorted(), teamLogos, teamClubColors)
}

func exportCSV(stats: LeagueStats, teams: [String], matchdays: [Int], to path: String) throws {
    var lines: [String] = []
    let header = ["Team"] + matchdays.map { "MD\($0)" }
    lines.append(header.joined(separator: ","))

    for team in teams {
        var row: [String] = [team]
        for md in matchdays {
            if let stat = stats[team]?[md] {
                row.append("\(stat.points),\(stat.gf),\(stat.ga)")
            } else {
                row.append(",,")
            }
        }
        lines.append(row.joined(separator: ","))
    }

    let csv = lines.joined(separator: "\n")
    try csv.write(to: URL(fileURLWithPath: path), atomically: true, encoding: .utf8)
}

struct FootballStatsCLI {
    static func extract() async {
        do {
            // Fetch teams and matches separately for optimization
            let teams = try await fetchTeams()
            let matches = try await fetchMatches()
            let (stats, teamNames, matchdays, _, _) = processData(matches, teams)

            let fileName = "\(league)_\(season)_history.csv"
            let filePath = FileManager.default.currentDirectoryPath + "/" + fileName
            try exportCSV(stats: stats, teams: teamNames, matchdays: matchdays, to: filePath)

            print("✅ Exported to \(filePath)")
        } catch {
            print("❌ Error: \(error)")
        }
    }

    // Test function to debug API response
    static func testAPI() async {
        print("🔍 Testing Teams API...")
        guard let teamsUrl = URL(string: "\(baseURL)/competitions/\(league)/teams?season=\(season)") else {
            print("❌ Bad Teams URL")
            return
        }

        var teamsRequest = URLRequest(url: teamsUrl)
        headers.forEach { key, value in teamsRequest.setValue(value, forHTTPHeaderField: key) }

        do {
            let (teamsData, teamsResponse) = try await URLSession.shared.data(for: teamsRequest)

            if let httpResponse = teamsResponse as? HTTPURLResponse {
                print("Teams HTTP Status: \(httpResponse.statusCode)")
            }

            if let jsonString = String(data: teamsData, encoding: .utf8) {
                print("Teams API Response (first 1000 chars): \(String(jsonString.prefix(1000)))")
            }
        } catch {
            print("❌ Teams Network Error: \(error)")
        }

        print("\n🔍 Testing Matches API...")
        guard let matchesUrl = URL(string: "\(baseURL)/competitions/\(league)/matches?season=\(season)") else {
            print("❌ Bad Matches URL")
            return
        }

        var matchesRequest = URLRequest(url: matchesUrl)
        headers.forEach { key, value in matchesRequest.setValue(value, forHTTPHeaderField: key) }

        do {
            let (matchesData, matchesResponse) = try await URLSession.shared.data(for: matchesRequest)

            if let httpResponse = matchesResponse as? HTTPURLResponse {
                print("Matches HTTP Status: \(httpResponse.statusCode)")
            }

            if let jsonString = String(data: matchesData, encoding: .utf8) {
                print("Matches API Response (first 1000 chars): \(String(jsonString.prefix(1000)))")
            }
        } catch {
            print("❌ Matches Network Error: \(error)")
        }
    }
}

// MARK: - Data Manager for SwiftUI Views
@MainActor
class FootballDataManager: ObservableObject {
    @Published var leagueStats: LeagueStats = [:]
    @Published var teams: [String] = []
    @Published var matchdays: [Int] = []
    @Published var teamLogos: [String: String] = [:] // Team name -> Logo URL
    @Published var teamColors: [String: String] = [:] // Team name -> Club colors
    @Published var isLoading = false
    @Published var errorMessage: String?

    func loadData() async {
        isLoading = true
        errorMessage = nil

        do {
            // Optimized: fetch teams and matches separately
            let teams = try await fetchTeams()
            let matches = try await fetchMatches()
            let (stats, teamList, matchdayList, logos, colors) = processData(matches, teams)

            self.leagueStats = stats
            self.teams = teamList
            self.matchdays = matchdayList
            self.teamLogos = logos
            self.teamColors = colors

            print("Loaded data: \(teamList.count) teams, \(matchdayList.count) matchdays")
            print("Team logos: \(logos)")
            print("Team colors: \(colors)")
        } catch {
            print("API failed, using sample data: \(error)")
            self.errorMessage = "API failed, using sample data: \(error.localizedDescription)"
            loadSampleData()
        }

        isLoading = false
    }

    private func loadSampleData() {
        // Sample data for demonstration
        let sampleTeams = ["Arsenal", "Manchester City", "Liverpool", "Chelsea", "Newcastle United", "Manchester United"]
        let sampleMatchdays = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

        var sampleStats: LeagueStats = [:]

        for team in sampleTeams {
            sampleStats[team] = [:]
            var points = 0
            for (index, matchday) in sampleMatchdays.enumerated() {
                // Simulate some realistic point progression
                let pointsGained = Int.random(in: 0...3)
                points += pointsGained
                sampleStats[team]![matchday] = (points: points, gf: index * 2, ga: index)
            }
        }

        self.leagueStats = sampleStats
        self.teams = sampleTeams
        self.matchdays = sampleMatchdays
    }

    func getPointsForMatchday(_ matchday: Int) -> [Entry] {
        var entries: [Entry] = []

        for team in teams {
            let points = leagueStats[team]?[matchday]?.points ?? 0
            entries.append(Entry(id: team, value: Double(points)))
        }

        if !teams.isEmpty {
            print("Team names from API: \(teams)")
        }

        return entries.sorted { $0.value > $1.value }
    }
}
