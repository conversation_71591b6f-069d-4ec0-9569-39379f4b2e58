import SwiftUI
//
//// MARK: – Model ----------------------------------------------------------
//
struct Entry: Identifiable, Equatable {
    let id: String          // stable identity (== name)
    var value: Double
}
//
//// Authentic Premier League team colors
let teamColors: [String: Color] = [
    "Arsenal": Color(red: 0.89, green: 0.0, blue: 0.15), // Arsenal red
    "Chelsea": Color(red: 0.0, green: 0.28, blue: 0.73), // Chelsea blue
    "Liverpool": Color(red: 0.78, green: 0.0, blue: 0.16), // Liverpool red
    "Manchester City": Color(red: 0.42, green: 0.68, blue: 0.84), // Sky blue
    "Manchester United": Color(red: 0.89, green: 0.0, blue: 0.15), // United red
    "Tottenham Hotspur": Color(red: 0.05, green: 0.16, blue: 0.36), // Navy blue
    "Newcastle United": Color(red: 0.0, green: 0.0, blue: 0.0), // Black
    "Brighton & Hove Albion": Color(red: 0.0, green: 0.51, blue: 0.78), // Brighton blue
    "Aston Villa": Color(red: 0.4, green: 0.0, blue: 0.4), // Claret
    "West Ham United": Color(red: 0.48, green: 0.0, blue: 0.36), // Claret
    "Crystal Palace": Color(red: 0.0, green: 0.2, blue: 0.6), // Palace blue
    "Fulham": Color(red: 0.0, green: 0.0, blue: 0.0), // Black
    "Wolverhampton Wanderers": Color(red: 0.98, green: 0.65, blue: 0.0), // Gold/amber
    "Everton": Color(red: 0.0, green: 0.27, blue: 0.68), // Everton blue
    "Brentford": Color(red: 0.89, green: 0.0, blue: 0.15), // Red
    "Nottingham Forest": Color(red: 0.89, green: 0.0, blue: 0.15), // Red
    "Luton Town": Color(red: 0.98, green: 0.5, blue: 0.0), // Orange
    "Burnley": Color(red: 0.4, green: 0.0, blue: 0.4), // Claret
    "Sheffield United": Color(red: 0.89, green: 0.0, blue: 0.15), // Red
    "Bournemouth": Color(red: 0.89, green: 0.0, blue: 0.15), // Red
    "Leicester City": Color(red: 0.0, green: 0.2, blue: 0.6), // Blue
    "Leeds United": Color(red: 1.0, green: 1.0, blue: 1.0), // White
    "Southampton": Color(red: 0.89, green: 0.0, blue: 0.15), // Red
    "Norwich City": Color(red: 1.0, green: 0.8, blue: 0.0), // Yellow
    "Watford": Color(red: 1.0, green: 0.8, blue: 0.0), // Yellow
]

// Team patterns/gradients for teams with similar colors
struct TeamStyle {
    let primaryColor: Color
    let secondaryColor: Color?
    let pattern: String // "solid", "stripe", "gradient"
}

let teamStyles: [String: TeamStyle] = [
    "Arsenal": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: nil, pattern: "solid"),
    "Chelsea": TeamStyle(primaryColor: Color(red: 0.0, green: 0.28, blue: 0.73), secondaryColor: nil, pattern: "solid"),
    "Liverpool": TeamStyle(primaryColor: Color(red: 0.78, green: 0.0, blue: 0.16), secondaryColor: Color(red: 1.0, green: 0.84, blue: 0.0), pattern: "gradient"), // Red with gold accent
    "Manchester City": TeamStyle(primaryColor: Color(red: 0.42, green: 0.68, blue: 0.84), secondaryColor: nil, pattern: "solid"),
    "Manchester United": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: Color(red: 1.0, green: 0.84, blue: 0.0), pattern: "gradient"), // Red with gold
    "Tottenham Hotspur": TeamStyle(primaryColor: Color(red: 0.05, green: 0.16, blue: 0.36), secondaryColor: Color.white, pattern: "gradient"),
    "Newcastle United": TeamStyle(primaryColor: Color.black, secondaryColor: Color.white, pattern: "stripe"),
    "Brighton & Hove Albion": TeamStyle(primaryColor: Color(red: 0.0, green: 0.51, blue: 0.78), secondaryColor: Color.white, pattern: "stripe"),
    "Aston Villa": TeamStyle(primaryColor: Color(red: 0.4, green: 0.0, blue: 0.4), secondaryColor: Color(red: 0.42, green: 0.68, blue: 0.84), pattern: "gradient"), // Claret and blue
    "West Ham United": TeamStyle(primaryColor: Color(red: 0.48, green: 0.0, blue: 0.36), secondaryColor: Color(red: 0.42, green: 0.68, blue: 0.84), pattern: "gradient"), // Claret and blue
    "Crystal Palace": TeamStyle(primaryColor: Color(red: 0.0, green: 0.2, blue: 0.6), secondaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), pattern: "stripe"), // Blue and red
    "Fulham": TeamStyle(primaryColor: Color.white, secondaryColor: Color.black, pattern: "stripe"),
    "Wolverhampton Wanderers": TeamStyle(primaryColor: Color(red: 0.98, green: 0.65, blue: 0.0), secondaryColor: Color.black, pattern: "gradient"),
    "Everton": TeamStyle(primaryColor: Color(red: 0.0, green: 0.27, blue: 0.68), secondaryColor: nil, pattern: "solid"),
    "Brentford": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: Color.white, pattern: "stripe"),
    "Nottingham Forest": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: Color.white, pattern: "stripe"),
    "Luton Town": TeamStyle(primaryColor: Color(red: 0.98, green: 0.5, blue: 0.0), secondaryColor: Color.white, pattern: "stripe"),
    "Burnley": TeamStyle(primaryColor: Color(red: 0.4, green: 0.0, blue: 0.4), secondaryColor: Color(red: 0.42, green: 0.68, blue: 0.84), pattern: "gradient"),
    "Sheffield United": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: Color.white, pattern: "stripe"),
    "Bournemouth": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: Color.black, pattern: "stripe"),
    "Leicester City": TeamStyle(primaryColor: Color(red: 0.0, green: 0.2, blue: 0.6), secondaryColor: Color(red: 1.0, green: 0.84, blue: 0.0), pattern: "gradient"), // Blue and gold
    "Leeds United": TeamStyle(primaryColor: Color.white, secondaryColor: Color(red: 1.0, green: 0.84, blue: 0.0), pattern: "gradient"),
    "Southampton": TeamStyle(primaryColor: Color(red: 0.89, green: 0.0, blue: 0.15), secondaryColor: Color.white, pattern: "stripe"),
    "Norwich City": TeamStyle(primaryColor: Color(red: 1.0, green: 0.8, blue: 0.0), secondaryColor: Color(red: 0.0, green: 0.6, blue: 0.0), pattern: "gradient"),
    "Watford": TeamStyle(primaryColor: Color(red: 1.0, green: 0.8, blue: 0.0), secondaryColor: Color.black, pattern: "stripe"),
]

// Short team name mapping
let teamShortNames: [String: String] = [
    "Arsenal": "ARS",
    "Chelsea": "CHE",
    "Liverpool": "LIV",
    "Manchester City": "MCI",
    "Manchester United": "MUN",
    "Tottenham Hotspur": "TOT",
    "Newcastle United": "NEW",
    "Brighton & Hove Albion": "BHA",
    "Aston Villa": "AVL",
    "West Ham United": "WHU",
    "Crystal Palace": "CRY",
    "Fulham": "FUL",
    "Wolverhampton Wanderers": "WOL",
    "Everton": "EVE",
    "Brentford": "BRE",
    "Nottingham Forest": "NFO",
    "Luton Town": "LUT",
    "Burnley": "BUR",
    "Sheffield United": "SHU",
    "Bournemouth": "BOU",
    "Leicester City": "LEI",
    "Leeds United": "LEE",
    "Southampton": "SOU",
    "Norwich City": "NOR",
    "Watford": "WAT",
]

// Fallback colors for teams not in the dictionary
let fallbackColors: [Color] = [
    Color(red: 0.2, green: 0.6, blue: 0.2), // Green
    Color(red: 0.6, green: 0.2, blue: 0.6), // Purple
    Color(red: 0.6, green: 0.4, blue: 0.0), // Brown
    Color(red: 0.0, green: 0.6, blue: 0.6), // Teal
    Color(red: 0.6, green: 0.0, blue: 0.6), // Magenta
    Color(red: 0.4, green: 0.4, blue: 0.4), // Gray
    Color(red: 0.8, green: 0.4, blue: 0.0), // Orange
    Color(red: 0.0, green: 0.4, blue: 0.0), // Dark green
]

// Parse club colors from API (e.g., "Red / White" -> primary color)
func parseClubColor(_ clubColors: String?) -> Color? {
    guard let colors = clubColors?.lowercased() else { return nil }

    // Extract primary color (first color mentioned)
    let separators = CharacterSet(charactersIn: "/,&")
    var colorComponents = colors.components(separatedBy: separators).map { $0.trimmingCharacters(in: .whitespaces) }

    // Also handle "and" as separator
    if colorComponents.count == 1 {
        colorComponents = colors.components(separatedBy: " and ").map { $0.trimmingCharacters(in: .whitespaces) }
    }

    guard let primaryColor = colorComponents.first else { return nil }

    switch primaryColor {
    case let color where color.contains("red"):
        return Color(red: 0.89, green: 0.0, blue: 0.15)
    case let color where color.contains("blue"):
        return Color(red: 0.0, green: 0.28, blue: 0.73)
    case let color where color.contains("green"):
        return Color(red: 0.0, green: 0.6, blue: 0.0)
    case let color where color.contains("yellow") || color.contains("gold"):
        return Color(red: 1.0, green: 0.84, blue: 0.0)
    case let color where color.contains("orange"):
        return Color(red: 1.0, green: 0.5, blue: 0.0)
    case let color where color.contains("purple") || color.contains("violet"):
        return Color(red: 0.5, green: 0.0, blue: 0.5)
    case let color where color.contains("black"):
        return Color.black
    case let color where color.contains("white"):
        return Color(red: 0.9, green: 0.9, blue: 0.9) // Light gray instead of pure white
    case let color where color.contains("navy"):
        return Color(red: 0.0, green: 0.0, blue: 0.5)
    case let color where color.contains("sky"):
        return Color(red: 0.42, green: 0.68, blue: 0.84)
    case let color where color.contains("claret") || color.contains("maroon"):
        return Color(red: 0.5, green: 0.0, blue: 0.3)
    default:
        return nil
    }
}

@MainActor func getTeamColor(_ teamName: String, dataManager: FootballDataManager? = nil) -> Color {
    // First try to get color from API data
    if let dataManager = dataManager,
       let clubColors = dataManager.teamColors[teamName],
       let apiColor = parseClubColor(clubColors) {
        return apiColor
    }

    // Fallback to hardcoded colors
    if let color = teamColors[teamName] {
        return color
    } else {
        // Use hash of team name to get consistent fallback color
        let hash = abs(teamName.hashValue)
        let colorIndex = hash % fallbackColors.count
        return fallbackColors[colorIndex]
    }
}

// Team logos using SF Symbols and Unicode
let teamLogos: [String: String] = [
    "Arsenal": "⚔️", // Sword for Gunners
    "Chelsea": "🦁", // Lion for Chelsea
    "Liverpool": "🔴", // Red circle for Reds
    "Manchester City": "🔵", // Blue circle for Citizens
    "Manchester United": "👹", // Red devil
    "Tottenham Hotspur": "🐓", // Cockerel/Rooster
    "Newcastle United": "⚫", // Black and white
    "Brighton & Hove Albion": "🌊", // Seagulls/waves
    "Aston Villa": "🦁", // Lion
    "West Ham United": "🔨", // Hammers
    "Crystal Palace": "🦅", // Eagle
    "Fulham": "⚪", // White
    "Wolverhampton Wanderers": "🐺", // Wolf
    "Everton": "🔵", // Blue
    "Brentford": "🐝", // Bees
    "Nottingham Forest": "🌲", // Tree/Forest
    "Luton Town": "🎩", // Hat
    "Burnley": "🔥", // Fire/Clarets
    "Sheffield United": "⚔️", // Blades
    "Bournemouth": "🍒", // Cherries
    "Leicester City": "🦊", // Fox
    "Leeds United": "⚪", // White
    "Southampton": "⚪", // Saints
    "Norwich City": "🐤", // Canaries
    "Watford": "🐝", // Hornets
]

func getShortTeamName(_ teamName: String) -> String {
    return teamShortNames[teamName] ?? String(teamName.prefix(3)).uppercased()
}

func getTeamLogo(_ teamName: String) -> String {
    // First try exact match
    if let logo = teamLogos[teamName] {
        return logo
    }

    // Try partial matches for common variations
    let lowercaseName = teamName.lowercased()

    if lowercaseName.contains("arsenal") { return "⚔️" }
    if lowercaseName.contains("chelsea") { return "🦁" }
    if lowercaseName.contains("liverpool") { return "🔴" }
    if lowercaseName.contains("manchester city") || lowercaseName.contains("man city") { return "🔵" }
    if lowercaseName.contains("manchester united") || lowercaseName.contains("man united") { return "👹" }
    if lowercaseName.contains("tottenham") || lowercaseName.contains("spurs") { return "🐓" }
    if lowercaseName.contains("newcastle") { return "⚫" }
    if lowercaseName.contains("brighton") { return "🌊" }
    if lowercaseName.contains("aston villa") { return "🦁" }
    if lowercaseName.contains("west ham") { return "🔨" }
    if lowercaseName.contains("crystal palace") { return "🦅" }
    if lowercaseName.contains("fulham") { return "⚪" }
    if lowercaseName.contains("wolves") || lowercaseName.contains("wolverhampton") { return "🐺" }
    if lowercaseName.contains("everton") { return "🔵" }
    if lowercaseName.contains("brentford") { return "🐝" }
    if lowercaseName.contains("nottingham") || lowercaseName.contains("forest") { return "🌲" }
    if lowercaseName.contains("luton") { return "🎩" }
    if lowercaseName.contains("burnley") { return "🔥" }
    if lowercaseName.contains("sheffield") { return "⚔️" }
    if lowercaseName.contains("bournemouth") { return "🍒" }
    if lowercaseName.contains("leicester") { return "🦊" }
    if lowercaseName.contains("leeds") { return "⚪" }
    if lowercaseName.contains("southampton") { return "⚪" }
    if lowercaseName.contains("norwich") { return "🐤" }
    if lowercaseName.contains("watford") { return "🐝" }

    // Debug: print unknown team names
    print("Unknown team name: '\(teamName)'")
    return "⚽"
}
