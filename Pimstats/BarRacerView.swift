import SwiftUI
//
//// MARK: – Model ----------------------------------------------------------
//
struct Entry: Identifiable, Equatable {
    let id: String          // stable identity (== name)
    var value: Double
}
//
//// Team colors for Premier League teams
let teamColors: [String: Color] = [
    "Arsenal": .red,
    "Chelsea": .blue,
    "Liverpool": .red,
    "Manchester City": .cyan,
    "Manchester United": .red,
    "Tottenham Hotspur": .white,
    "Newcastle United": .black,
    "Brighton & Hove Albion": .blue,
    "Aston Villa": .purple,
    "West Ham United": .blue,
    "Crystal Palace": .blue,
    "Fulham": .white,
    "Wolverhampton Wanderers": .orange,
    "Everton": .blue,
    "Brentford": .red,
    "Nottingham Forest": .red,
    "Luton Town": .orange,
    "Burnley": .red,
    "Sheffield United": .red,
    "Bournemouth": .red
]

func getTeamColor(_ teamName: String) -> Color {
    return teamColors[teamName] ?? Color.gray
}
