import SwiftUI
//
//// MARK: – Model ----------------------------------------------------------
//
struct Entry: Identifiable, Equatable {
    let id: String          // stable identity (== name)
    var value: Double
}
//
//// Team colors for Premier League teams - more distinct colors
let teamColors: [String: Color] = [
    "Arsenal": Color(red: 0.8, green: 0.0, blue: 0.0), // Dark red
    "Chelsea": Color(red: 0.0, green: 0.2, blue: 0.8), // Royal blue
    "Liverpool": Color(red: 0.7, green: 0.0, blue: 0.0), // Liverpool red
    "Manchester City": Color(red: 0.4, green: 0.8, blue: 1.0), // Sky blue
    "Manchester United": Color(red: 1.0, green: 0.0, blue: 0.0), // Bright red
    "Tottenham Hotspur": Color(red: 0.0, green: 0.0, blue: 0.4), // Navy blue
    "Newcastle United": Color(red: 0.0, green: 0.0, blue: 0.0), // Black
    "Brighton & Hove Albion": Color(red: 0.0, green: 0.4, blue: 0.8), // Brighton blue
    "Aston Villa": Color(red: 0.5, green: 0.0, blue: 0.5), // Claret
    "West Ham United": Color(red: 0.5, green: 0.0, blue: 0.3), // Claret and blue
    "Crystal Palace": Color(red: 0.0, green: 0.3, blue: 0.7), // Palace blue
    "Fulham": Color(red: 0.0, green: 0.0, blue: 0.0), // Black
    "Wolverhampton Wanderers": Color(red: 1.0, green: 0.6, blue: 0.0), // Gold
    "Everton": Color(red: 0.0, green: 0.2, blue: 0.6), // Everton blue
    "Brentford": Color(red: 0.8, green: 0.0, blue: 0.2), // Brentford red
    "Nottingham Forest": Color(red: 0.6, green: 0.0, blue: 0.0), // Forest red
    "Luton Town": Color(red: 1.0, green: 0.5, blue: 0.0), // Orange
    "Burnley": Color(red: 0.5, green: 0.0, blue: 0.2), // Burnley claret
    "Sheffield United": Color(red: 0.8, green: 0.0, blue: 0.3), // Sheffield red
    "Bournemouth": Color(red: 0.8, green: 0.0, blue: 0.0), // Cherries red
    "Leicester City": Color(red: 0.0, green: 0.2, blue: 0.8), // Leicester blue
    "Leeds United": Color(red: 1.0, green: 1.0, blue: 0.0), // Leeds yellow
    "Southampton": Color(red: 0.8, green: 0.0, blue: 0.0), // Saints red
    "Norwich City": Color(red: 1.0, green: 1.0, blue: 0.0), // Canaries yellow
    "Watford": Color(red: 1.0, green: 1.0, blue: 0.0), // Hornets yellow
]

// Short team name mapping
let teamShortNames: [String: String] = [
    "Arsenal": "ARS",
    "Chelsea": "CHE",
    "Liverpool": "LIV",
    "Manchester City": "MCI",
    "Manchester United": "MUN",
    "Tottenham Hotspur": "TOT",
    "Newcastle United": "NEW",
    "Brighton & Hove Albion": "BHA",
    "Aston Villa": "AVL",
    "West Ham United": "WHU",
    "Crystal Palace": "CRY",
    "Fulham": "FUL",
    "Wolverhampton Wanderers": "WOL",
    "Everton": "EVE",
    "Brentford": "BRE",
    "Nottingham Forest": "NFO",
    "Luton Town": "LUT",
    "Burnley": "BUR",
    "Sheffield United": "SHU",
    "Bournemouth": "BOU",
    "Leicester City": "LEI",
    "Leeds United": "LEE",
    "Southampton": "SOU",
    "Norwich City": "NOR",
    "Watford": "WAT",
]

// Fallback colors for teams not in the dictionary
let fallbackColors: [Color] = [
    Color(red: 0.2, green: 0.6, blue: 0.2), // Green
    Color(red: 0.6, green: 0.2, blue: 0.6), // Purple
    Color(red: 0.6, green: 0.4, blue: 0.0), // Brown
    Color(red: 0.0, green: 0.6, blue: 0.6), // Teal
    Color(red: 0.6, green: 0.0, blue: 0.6), // Magenta
    Color(red: 0.4, green: 0.4, blue: 0.4), // Gray
    Color(red: 0.8, green: 0.4, blue: 0.0), // Orange
    Color(red: 0.0, green: 0.4, blue: 0.0), // Dark green
]

func getTeamColor(_ teamName: String) -> Color {
    if let color = teamColors[teamName] {
        return color
    } else {
        // Use hash of team name to get consistent fallback color
        let hash = abs(teamName.hashValue)
        let colorIndex = hash % fallbackColors.count
        return fallbackColors[colorIndex]
    }
}

// Team logos using SF Symbols and Unicode
let teamLogos: [String: String] = [
    "Arsenal": "⚔️", // Sword for Gunners
    "Chelsea": "🦁", // Lion for Chelsea
    "Liverpool": "🔴", // Red circle for Reds
    "Manchester City": "🔵", // Blue circle for Citizens
    "Manchester United": "👹", // Red devil
    "Tottenham Hotspur": "🐓", // Cockerel/Rooster
    "Newcastle United": "⚫", // Black and white
    "Brighton & Hove Albion": "🌊", // Seagulls/waves
    "Aston Villa": "🦁", // Lion
    "West Ham United": "🔨", // Hammers
    "Crystal Palace": "🦅", // Eagle
    "Fulham": "⚪", // White
    "Wolverhampton Wanderers": "🐺", // Wolf
    "Everton": "🔵", // Blue
    "Brentford": "🐝", // Bees
    "Nottingham Forest": "🌲", // Tree/Forest
    "Luton Town": "🎩", // Hat
    "Burnley": "🔥", // Fire/Clarets
    "Sheffield United": "⚔️", // Blades
    "Bournemouth": "🍒", // Cherries
    "Leicester City": "🦊", // Fox
    "Leeds United": "⚪", // White
    "Southampton": "⚪", // Saints
    "Norwich City": "🐤", // Canaries
    "Watford": "🐝", // Hornets
]

func getShortTeamName(_ teamName: String) -> String {
    return teamShortNames[teamName] ?? String(teamName.prefix(3)).uppercased()
}

func getTeamLogo(_ teamName: String) -> String {
    // First try exact match
    if let logo = teamLogos[teamName] {
        return logo
    }

    // Try partial matches for common variations
    let lowercaseName = teamName.lowercased()

    if lowercaseName.contains("arsenal") { return "⚔️" }
    if lowercaseName.contains("chelsea") { return "🦁" }
    if lowercaseName.contains("liverpool") { return "🔴" }
    if lowercaseName.contains("manchester city") || lowercaseName.contains("man city") { return "🔵" }
    if lowercaseName.contains("manchester united") || lowercaseName.contains("man united") { return "👹" }
    if lowercaseName.contains("tottenham") || lowercaseName.contains("spurs") { return "🐓" }
    if lowercaseName.contains("newcastle") { return "⚫" }
    if lowercaseName.contains("brighton") { return "🌊" }
    if lowercaseName.contains("aston villa") { return "🦁" }
    if lowercaseName.contains("west ham") { return "🔨" }
    if lowercaseName.contains("crystal palace") { return "🦅" }
    if lowercaseName.contains("fulham") { return "⚪" }
    if lowercaseName.contains("wolves") || lowercaseName.contains("wolverhampton") { return "🐺" }
    if lowercaseName.contains("everton") { return "🔵" }
    if lowercaseName.contains("brentford") { return "🐝" }
    if lowercaseName.contains("nottingham") || lowercaseName.contains("forest") { return "🌲" }
    if lowercaseName.contains("luton") { return "🎩" }
    if lowercaseName.contains("burnley") { return "🔥" }
    if lowercaseName.contains("sheffield") { return "⚔️" }
    if lowercaseName.contains("bournemouth") { return "🍒" }
    if lowercaseName.contains("leicester") { return "🦊" }
    if lowercaseName.contains("leeds") { return "⚪" }
    if lowercaseName.contains("southampton") { return "⚪" }
    if lowercaseName.contains("norwich") { return "🐤" }
    if lowercaseName.contains("watford") { return "🐝" }

    // Debug: print unknown team names
    print("Unknown team name: '\(teamName)'")
    return "⚽"
}
