import SwiftUI
import Charts
import Combine

// MARK: – Model ----------------------------------------------------------

struct Entry: Identifiable, Equatable {
    let id: String          // stable identity (== name)
    var value: Double
}

// Sample matrix (rows = time-steps, cols = entries)
let matrix: [[Double]] = [
    [10,  7,  3,  1],
    [14,  15,  4,  2],
    [18, 11,  6,  3],
    [20, 22,  9,  4],
    [23, 18, 11,  45]
]
let names = ["Red", "Blue", "Green", "Yellow"]
let timeLabels = ["2019","2020","2021","2022","2023"]

let entryColors: [String: Color] = [
    "Red": .red,
    "Blue": .blue,
    "Green": .green,
    "Yellow": .yellow
]

func snapshot(row: [Double]) -> [Entry] {
    zip(names, row).map { Entry(id: $0.0, value: $0.1) }
}

// MARK: – View -----------------------------------------------------------

struct BarAndPieRaceView: View {
    // shared state → mutate, don’t recreate
    @State private var bars: [Entry] = snapshot(row: matrix.first!)
    @State private var frameIndex = 0
    
    private let ticker = Timer.publish(every: 1,
                                       on: .main,
                                       in: .common).autoconnect()
    
    var bar: some View {
        // 1️⃣  BAR-RACE  ────────────────────────────────────────────
        Chart {
            ForEach(bars.sorted { $0.value > $1.value }) { entry in
                BarMark(
                    x: .value("Value", entry.value),
                    y: .value("Entry", entry.id)
                )
                .annotation(position: .trailing) {
                    Text(Int(entry.value).formatted())
                        .font(.caption.bold())
                }
                .foregroundStyle(entryColors[entry.id, default: .gray])
            }
        }
        .chartXAxis(.hidden)
        .frame(height: 220)
        .animation(.easeInOut(duration: 0.8), value: bars)
    }
    
    var pie: some View {
        // 2️⃣  PIE (donut)  ────────────────────────────────────────
        Chart {
            ForEach(bars) { entry in
                SectorMark(
                    angle: .value("Value", entry.value),
                    innerRadius: .ratio(0.50)          // donut hole
                )
                .cornerRadius(4)
                .foregroundStyle(entryColors[entry.id, default: .gray])
                // leader lines & labels animate automatically
                .annotation(position: .overlay) {
                    Text(entry.id)
//                        .foregroundStyle(.white.shadow(radius: 1))
                        .font(.caption2)
                }
            }
        }
        .chartLegend(.visible)
        .frame(height: 220)
        .animation(.easeInOut(duration: 0.8), value: bars)
    }
    
    var body: some View {
        VStack(spacing: 24) {
        
            bar
            pie
            
            // 3️⃣  TIME LABEL  ─────────────────────────────────────────
            Text(timeLabels[frameIndex])
                .font(.largeTitle.weight(.semibold))
                .transition(.opacity.combined(with: .scale))
                .id(frameIndex)
        }
        .padding(.vertical, 32)
        .onReceive(ticker) { _ in advanceFrame() }
    }
    
    
    // Advance one matrix row → mutate existing entries
    private func advanceFrame() {
        let next = (frameIndex + 1) % matrix.count
        let newValues = matrix[next]
        
        for i in bars.indices { bars[i].value = newValues[i] }
        frameIndex = next
    }
}



// MARK: – Preview --------------------------------------------------------

#Preview {
    BarAndPieRaceView()
        .frame(width: 380, height: 640)
}
