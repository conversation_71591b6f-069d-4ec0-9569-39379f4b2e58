import SwiftUI

struct ContentView: View {
    var body: some View {
        TabView {
            FootballBarRaceView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("Charts")
                }

            AdminPanelView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Admin")
                }
        }
    }
}

struct AdminPanelView: View {
    @StateObject private var dataManager = FootballDataManager()
    @State private var isExtracting = false
    @State private var extractionMessage = ""
    @State private var showAlert = false

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                Text("Admin Panel")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("Data Management")
                    .font(.title2)
                    .foregroundColor(.secondary)

                VStack(spacing: 20) {
                    Button(action: {
                        extractData()
                    }) {
                        HStack {
                            if isExtracting {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .padding(.trailing, 5)
                            } else {
                                Image(systemName: "square.and.arrow.down")
                                    .font(.title2)
                            }
                            Text(isExtracting ? "Extracting..." : "Extract Football Data")
                                .font(.headline)
                        }
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(isExtracting ? Color.gray : Color.blue)
                        .cornerRadius(10)
                    }
                    .disabled(isExtracting)

                    if !extractionMessage.isEmpty {
                        Text(extractionMessage)
                            .font(.body)
                            .foregroundColor(extractionMessage.contains("✅") ? .green : .red)
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                .padding(.horizontal)

                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
        .alert("Extraction Complete", isPresented: $showAlert) {
            Button("OK") { }
        } message: {
            Text(extractionMessage)
        }
    }

    private func extractData() {
        isExtracting = true
        extractionMessage = ""

        Task {
            // First extract to CSV
            await FootballStatsCLI.extract()

            // Then load the data into our data manager
            await dataManager.loadData()

            await MainActor.run {
                isExtracting = false
                if dataManager.errorMessage == nil {
                    extractionMessage = "✅ Data extraction and loading completed successfully!"
                } else {
                    extractionMessage = "⚠️ Data extracted but failed to load: \(dataManager.errorMessage ?? "Unknown error")"
                }
                showAlert = true
            }
        }
    }
}

#Preview {
    ContentView()
}
