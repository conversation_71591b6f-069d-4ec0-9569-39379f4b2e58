import SwiftUI
import Charts
import Combine

// MARK: – Team Logo View -----------------------------------------------

struct TeamLogo: View {
    let teamName: String
    let dataManager: FootballDataManager

    var body: some View {
        if let logoURL = dataManager.teamLogos[teamName],
           let url = URL(string: logoURL) {
            AsyncImage(url: url) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                // Fallback to colored circle while loading
                TeamCircle(teamName: teamName, dataManager: dataManager)
            }
            .frame(width: 16, height: 16)
            .clipShape(Circle())
        } else {
            // Fallback to colored circle if no logo URL
            TeamCircle(teamName: teamName, dataManager: dataManager)
        }
    }
}

struct TeamCircle: View {
    let teamName: String
    let dataManager: FootballDataManager?

    init(teamName: String, dataManager: FootballDataManager? = nil) {
        self.teamName = teamName
        self.dataManager = dataManager
    }

    var body: some View {
        let primaryColor = getTeamColor(teamName, dataManager: dataManager)
        let style = teamStyles[teamName] ?? TeamStyle(primaryColor: primaryColor, secondaryColor: nil, pattern: "solid")

        getTeamFill(style: style)
            .frame(width: 12, height: 12)
            .overlay(
                Circle()
                    .stroke(Color.black.opacity(0.3), lineWidth: 0.5)
            )
    }

    @ViewBuilder
    private func getTeamFill(style: TeamStyle) -> some View {
        switch style.pattern {
        case "gradient":
            if let secondary = style.secondaryColor {
                Circle()
                    .fill(LinearGradient(
                        colors: [style.primaryColor, secondary],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
            } else {
                Circle()
                    .fill(style.primaryColor)
            }
        case "stripe":
            // For stripes, we'll use a radial gradient to simulate the effect
            if let secondary = style.secondaryColor {
                Circle()
                    .fill(RadialGradient(
                        colors: [style.primaryColor, secondary, style.primaryColor],
                        center: .center,
                        startRadius: 2,
                        endRadius: 6
                    ))
            } else {
                Circle()
                    .fill(style.primaryColor)
            }
        default: // "solid"
            Circle()
                .fill(style.primaryColor)
        }
    }
}

// MARK: – View -----------------------------------------------------------

struct FootballBarRaceView: View {
    @StateObject private var dataManager = FootballDataManager()
    @State private var bars: [Entry] = []
    @State private var frameIndex = 0
    @State private var isAnimating = false
    
    private let ticker = Timer.publish(every: 2,
                                       on: .main,
                                       in: .common).autoconnect()
    
    var bar: some View {
        // 1️⃣  BAR-RACE  ────────────────────────────────────────────
        Chart {
            ForEach(bars.sorted { $0.value > $1.value }) { entry in
                BarMark(
                    x: .value("Points", entry.value),
                    y: .value("Team", entry.id),
                    height: .fixed(8) // Thin bars like lines
                )
                .annotation(position: .trailing) {
                    HStack(spacing: 6) {
                        TeamLogo(teamName: entry.id, dataManager: dataManager)
                        Text(Int(entry.value).formatted())
                            .font(.caption.bold())
                            .foregroundColor(.black)
                    }
                }
                .annotation(position: .topLeading) {
                    Text(getShortTeamName(entry.id))
                        .font(.caption.bold())
                        .foregroundColor(.black)
                }
                .foregroundStyle(getTeamColor(entry.id, dataManager: dataManager))
            }
        }
        .chartXAxis(.hidden)
        .chartYAxis(.hidden) // Hide Y-axis to remove separators
        .chartPlotStyle { plotArea in
            plotArea
                .background(.clear) // Remove background grid
        }
        .frame(height: min(CGFloat(bars.count * 35 + 50), 500))
        .animation(.easeInOut(duration: 1.5), value: bars)
    }

    var body: some View {
        VStack(spacing: 24) {
            if dataManager.isLoading {
                ProgressView("Loading football data...")
                    .font(.headline)
                    .padding()
            } else if let errorMessage = dataManager.errorMessage {
                VStack {
                    Text("Error loading data")
                        .font(.headline)
                        .foregroundColor(.red)
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Button("Retry") {
                        Task {
                            await dataManager.loadData()
                        }
                    }
                    .padding()
                }
            } else if !bars.isEmpty {
                bar

                // 3️⃣  MATCHDAY LABEL  ─────────────────────────────────────────
                if frameIndex < dataManager.matchdays.count {
                    Text("Matchday \(dataManager.matchdays[frameIndex])")
                        .font(.largeTitle.weight(.semibold))
                        .transition(.opacity.combined(with: .scale))
                        .id(frameIndex)
                }
                
                // Control buttons
                HStack {
                    Button(isAnimating ? "Pause" : "Play") {
                        isAnimating.toggle()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    
                    Button("Reset") {
                        frameIndex = 0
                        updateBarsForCurrentFrame()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            } else {
                Text("No data available")
                    .font(.headline)
                    .foregroundColor(.secondary)
                Button("Load Data") {
                    Task {
                        await dataManager.loadData()
                    }
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding(.vertical, 32)
        .onReceive(ticker) { _ in 
            if isAnimating {
                advanceFrame()
            }
        }
        .onAppear {
            Task {
                await dataManager.loadData()
            }
        }
        .onChange(of: dataManager.matchdays) { _, _ in
            updateBarsForCurrentFrame()
        }
    }
    
    // Advance one matchday → update entries with new points
    private func advanceFrame() {
        guard !dataManager.matchdays.isEmpty else { return }
        frameIndex = (frameIndex + 1) % dataManager.matchdays.count
        updateBarsForCurrentFrame()
    }
    
    private func updateBarsForCurrentFrame() {
        guard frameIndex < dataManager.matchdays.count else { return }
        let currentMatchday = dataManager.matchdays[frameIndex]
        bars = dataManager.getPointsForMatchday(currentMatchday)
    }
}

// MARK: – Preview --------------------------------------------------------

#Preview {
    FootballBarRaceView()
        .frame(width: 380, height: 640)
}
