import SwiftUI
import Charts
import Combine

// MARK: – View -----------------------------------------------------------

struct FootballBarRaceView: View {
    @StateObject private var dataManager = FootballDataManager()
    @State private var bars: [Entry] = []
    @State private var frameIndex = 0
    @State private var isAnimating = false
    
    private let ticker = Timer.publish(every: 2,
                                       on: .main,
                                       in: .common).autoconnect()
    
    var bar: some View {
        // 1️⃣  BAR-RACE  ────────────────────────────────────────────
        Chart {
            ForEach(bars.sorted { $0.value > $1.value }) { entry in
                BarMark(
                    x: .value("Points", entry.value),
                    y: .value("Team", entry.id)
                )
                .annotation(position: .trailing) {
                    Text(Int(entry.value).formatted())
                        .font(.caption.bold())
                }
                .foregroundStyle(getTeamColor(entry.id))
            }
        }
        .chartXAxis(.hidden)
        .frame(height: min(CGFloat(bars.count * 25 + 50), 400))
        .animation(.easeInOut(duration: 1.5), value: bars)
    }
    
    var pie: some View {
        // 2️⃣  PIE (donut)  ────────────────────────────────────────
        Chart {
            ForEach(bars.prefix(8)) { entry in // Show top 8 teams in pie chart
                SectorMark(
                    angle: .value("Points", entry.value),
                    innerRadius: .ratio(0.50)          // donut hole
                )
                .cornerRadius(4)
                .foregroundStyle(getTeamColor(entry.id))
                .annotation(position: .overlay) {
                    if entry.value > 0 {
                        Text(String(entry.id.prefix(3)))
                            .font(.caption2.bold())
                            .foregroundColor(.white)
                    }
                }
            }
        }
        .chartLegend(.hidden)
        .frame(height: 220)
        .animation(.easeInOut(duration: 1.5), value: bars)
    }
    
    var body: some View {
        VStack(spacing: 24) {
            if dataManager.isLoading {
                ProgressView("Loading football data...")
                    .font(.headline)
                    .padding()
            } else if let errorMessage = dataManager.errorMessage {
                VStack {
                    Text("Error loading data")
                        .font(.headline)
                        .foregroundColor(.red)
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Button("Retry") {
                        Task {
                            await dataManager.loadData()
                        }
                    }
                    .padding()
                }
            } else if !bars.isEmpty {
                bar                
                // 3️⃣  MATCHDAY LABEL  ─────────────────────────────────────────
                if frameIndex < dataManager.matchdays.count {
                    Text("Matchday \(dataManager.matchdays[frameIndex])")
                        .font(.largeTitle.weight(.semibold))
                        .transition(.opacity.combined(with: .scale))
                        .id(frameIndex)
                }
                
                // Control buttons
                HStack {
                    Button(isAnimating ? "Pause" : "Play") {
                        isAnimating.toggle()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    
                    Button("Reset") {
                        frameIndex = 0
                        updateBarsForCurrentFrame()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            } else {
                Text("No data available")
                    .font(.headline)
                    .foregroundColor(.secondary)
                Button("Load Data") {
                    Task {
                        await dataManager.loadData()
                    }
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding(.vertical, 32)
        .onReceive(ticker) { _ in 
            if isAnimating {
                advanceFrame()
            }
        }
        .onAppear {
            Task {
                await dataManager.loadData()
            }
        }
        .onChange(of: dataManager.matchdays) { _, _ in
            updateBarsForCurrentFrame()
        }
    }
    
    // Advance one matchday → update entries with new points
    private func advanceFrame() {
        guard !dataManager.matchdays.isEmpty else { return }
        frameIndex = (frameIndex + 1) % dataManager.matchdays.count
        updateBarsForCurrentFrame()
    }
    
    private func updateBarsForCurrentFrame() {
        guard frameIndex < dataManager.matchdays.count else { return }
        let currentMatchday = dataManager.matchdays[frameIndex]
        bars = dataManager.getPointsForMatchday(currentMatchday)
    }
}

// MARK: – Preview --------------------------------------------------------

#Preview {
    FootballBarRaceView()
        .frame(width: 380, height: 640)
}
