import SwiftUI
import Charts
import Combine

// MARK: – View -----------------------------------------------------------

struct FootballBarRaceView: View {
    @StateObject private var dataManager = FootballDataManager()
    @State private var bars: [Entry] = []
    @State private var frameIndex = 0
    @State private var isAnimating = false
    @State private var isUpdatingValues = false

    private let ticker = Timer.publish(every: 3,
                                       on: .main,
                                       in: .common).autoconnect()
    
    var bar: some View {
        // 1️⃣  BAR-RACE  ────────────────────────────────────────────
        Chart {
            ForEach(isUpdatingValues ? bars : bars.sorted { $0.value > $1.value }) { entry in
                BarMark(
                    x: .value("Points", entry.value),
                    y: .value("Team", entry.id),
                    height: .fixed(8) // Thin bars like lines
                )
                .annotation(position: .trailing) {
                    Text(Int(entry.value).formatted())
                        .font(.caption.bold())
                        .foregroundColor(.black)
                }
                .annotation(position: .topLeading) {
                    Text(getShortTeamName(entry.id))
                        .font(.caption.bold())
                        .foregroundColor(.black)
                }
                .foregroundStyle(getTeamColor(entry.id))
            }
        }
        .chartXAxis(.hidden)
        .chartYAxis(.hidden) // Hide Y-axis to remove separators
        .chartPlotStyle { plotArea in
            plotArea
                .background(.clear) // Remove background grid
        }
        .frame(height: min(CGFloat(bars.count * 35 + 50), 500))
        .animation(.easeInOut(duration: isUpdatingValues ? 1.0 : 0.8), value: isUpdatingValues ? bars.map(\.value) : bars.map(\.id))
    }

    var body: some View {
        VStack(spacing: 24) {
            if dataManager.isLoading {
                ProgressView("Loading football data...")
                    .font(.headline)
                    .padding()
            } else if let errorMessage = dataManager.errorMessage {
                VStack {
                    Text("Error loading data")
                        .font(.headline)
                        .foregroundColor(.red)
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Button("Retry") {
                        Task {
                            await dataManager.loadData()
                        }
                    }
                    .padding()
                }
            } else if !bars.isEmpty {
                bar

                // 3️⃣  MATCHDAY LABEL  ─────────────────────────────────────────
                if frameIndex < dataManager.matchdays.count {
                    Text("Matchday \(dataManager.matchdays[frameIndex])")
                        .font(.largeTitle.weight(.semibold))
                        .transition(.opacity.combined(with: .scale))
                        .id(frameIndex)
                }
                
                // Control buttons
                HStack {
                    Button(isAnimating ? "Pause" : "Play") {
                        isAnimating.toggle()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    
                    Button("Reset") {
                        frameIndex = 0
                        updateBarsForCurrentFrame()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            } else {
                Text("No data available")
                    .font(.headline)
                    .foregroundColor(.secondary)
                Button("Load Data") {
                    Task {
                        await dataManager.loadData()
                    }
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding(.vertical, 32)
        .onReceive(ticker) { _ in 
            if isAnimating {
                advanceFrame()
            }
        }
        .onAppear {
            Task {
                await dataManager.loadData()
            }
        }
        .onChange(of: dataManager.matchdays) { _, _ in
            if !dataManager.matchdays.isEmpty && bars.isEmpty {
                // Initial load - no animation needed
                let currentMatchday = dataManager.matchdays[frameIndex]
                bars = dataManager.getPointsForMatchday(currentMatchday).sorted { $0.value > $1.value }
            }
        }
    }
    
    // Advance one matchday → update entries with new points
    private func advanceFrame() {
        guard !dataManager.matchdays.isEmpty else { return }
        frameIndex = (frameIndex + 1) % dataManager.matchdays.count
        updateBarsForCurrentFrame()
    }

    private func updateBarsForCurrentFrame() {
        guard frameIndex < dataManager.matchdays.count else { return }
        let currentMatchday = dataManager.matchdays[frameIndex]
        let newData = dataManager.getPointsForMatchday(currentMatchday)

        // Stage 1: Update values while keeping current order
        isUpdatingValues = true

        // Update the values of existing bars to match new data
        for i in bars.indices {
            if let newEntry = newData.first(where: { $0.id == bars[i].id }) {
                bars[i].value = newEntry.value
            }
        }

        // Add any new teams that weren't in the previous data
        for newEntry in newData {
            if !bars.contains(where: { $0.id == newEntry.id }) {
                bars.append(newEntry)
            }
        }

        // Stage 2: After value animation completes, sort the bars
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) {
            withAnimation(.easeInOut(duration: 0.8)) {
                isUpdatingValues = false
                bars = newData.sorted { $0.value > $1.value }
            }
        }
    }
}

// MARK: – Preview --------------------------------------------------------

#Preview {
    FootballBarRaceView()
        .frame(width: 380, height: 640)
}
