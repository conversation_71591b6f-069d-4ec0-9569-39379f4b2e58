// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		8EBD9AA72DFE12450048008B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8EBD9A8F2DFE12430048008B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8EBD9A962DFE12430048008B;
			remoteInfo = Pimstats;
		};
		8EBD9AB12DFE12450048008B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8EBD9A8F2DFE12430048008B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8EBD9A962DFE12430048008B;
			remoteInfo = Pimstats;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8EBD9A972DFE12430048008B /* Pimstats.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Pimstats.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8EBD9AA62DFE12450048008B /* PimstatsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PimstatsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8EBD9AB02DFE12450048008B /* PimstatsUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PimstatsUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8EBD9A992DFE12430048008B /* Pimstats */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Pimstats;
			sourceTree = "<group>";
		};
		8EBD9AA92DFE12450048008B /* PimstatsTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PimstatsTests;
			sourceTree = "<group>";
		};
		8EBD9AB32DFE12450048008B /* PimstatsUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PimstatsUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8EBD9A942DFE12430048008B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBD9AA32DFE12450048008B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBD9AAD2DFE12450048008B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8EBD9A8E2DFE12430048008B = {
			isa = PBXGroup;
			children = (
				8EBD9A992DFE12430048008B /* Pimstats */,
				8EBD9AA92DFE12450048008B /* PimstatsTests */,
				8EBD9AB32DFE12450048008B /* PimstatsUITests */,
				8EBD9A982DFE12430048008B /* Products */,
			);
			sourceTree = "<group>";
		};
		8EBD9A982DFE12430048008B /* Products */ = {
			isa = PBXGroup;
			children = (
				8EBD9A972DFE12430048008B /* Pimstats.app */,
				8EBD9AA62DFE12450048008B /* PimstatsTests.xctest */,
				8EBD9AB02DFE12450048008B /* PimstatsUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8EBD9A962DFE12430048008B /* Pimstats */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8EBD9ABA2DFE12450048008B /* Build configuration list for PBXNativeTarget "Pimstats" */;
			buildPhases = (
				8EBD9A932DFE12430048008B /* Sources */,
				8EBD9A942DFE12430048008B /* Frameworks */,
				8EBD9A952DFE12430048008B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8EBD9A992DFE12430048008B /* Pimstats */,
			);
			name = Pimstats;
			packageProductDependencies = (
			);
			productName = Pimstats;
			productReference = 8EBD9A972DFE12430048008B /* Pimstats.app */;
			productType = "com.apple.product-type.application";
		};
		8EBD9AA52DFE12450048008B /* PimstatsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8EBD9ABD2DFE12450048008B /* Build configuration list for PBXNativeTarget "PimstatsTests" */;
			buildPhases = (
				8EBD9AA22DFE12450048008B /* Sources */,
				8EBD9AA32DFE12450048008B /* Frameworks */,
				8EBD9AA42DFE12450048008B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8EBD9AA82DFE12450048008B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8EBD9AA92DFE12450048008B /* PimstatsTests */,
			);
			name = PimstatsTests;
			packageProductDependencies = (
			);
			productName = PimstatsTests;
			productReference = 8EBD9AA62DFE12450048008B /* PimstatsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8EBD9AAF2DFE12450048008B /* PimstatsUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8EBD9AC02DFE12450048008B /* Build configuration list for PBXNativeTarget "PimstatsUITests" */;
			buildPhases = (
				8EBD9AAC2DFE12450048008B /* Sources */,
				8EBD9AAD2DFE12450048008B /* Frameworks */,
				8EBD9AAE2DFE12450048008B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8EBD9AB22DFE12450048008B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8EBD9AB32DFE12450048008B /* PimstatsUITests */,
			);
			name = PimstatsUITests;
			packageProductDependencies = (
			);
			productName = PimstatsUITests;
			productReference = 8EBD9AB02DFE12450048008B /* PimstatsUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8EBD9A8F2DFE12430048008B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					8EBD9A962DFE12430048008B = {
						CreatedOnToolsVersion = 16.3;
					};
					8EBD9AA52DFE12450048008B = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 8EBD9A962DFE12430048008B;
					};
					8EBD9AAF2DFE12450048008B = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 8EBD9A962DFE12430048008B;
					};
				};
			};
			buildConfigurationList = 8EBD9A922DFE12430048008B /* Build configuration list for PBXProject "Pimstats" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8EBD9A8E2DFE12430048008B;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8EBD9A982DFE12430048008B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8EBD9A962DFE12430048008B /* Pimstats */,
				8EBD9AA52DFE12450048008B /* PimstatsTests */,
				8EBD9AAF2DFE12450048008B /* PimstatsUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8EBD9A952DFE12430048008B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBD9AA42DFE12450048008B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBD9AAE2DFE12450048008B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8EBD9A932DFE12430048008B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBD9AA22DFE12450048008B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8EBD9AAC2DFE12450048008B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8EBD9AA82DFE12450048008B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8EBD9A962DFE12430048008B /* Pimstats */;
			targetProxy = 8EBD9AA72DFE12450048008B /* PBXContainerItemProxy */;
		};
		8EBD9AB22DFE12450048008B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8EBD9A962DFE12430048008B /* Pimstats */;
			targetProxy = 8EBD9AB12DFE12450048008B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8EBD9AB82DFE12450048008B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8EBD9AB92DFE12450048008B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8EBD9ABB2DFE12450048008B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.Pimstats;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8EBD9ABC2DFE12450048008B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.Pimstats;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		8EBD9ABE2DFE12450048008B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Pimstats.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Pimstats";
			};
			name = Debug;
		};
		8EBD9ABF2DFE12450048008B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Pimstats.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Pimstats";
			};
			name = Release;
		};
		8EBD9AC12DFE12450048008B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Pimstats;
			};
			name = Debug;
		};
		8EBD9AC22DFE12450048008B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Pimstats;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8EBD9A922DFE12430048008B /* Build configuration list for PBXProject "Pimstats" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EBD9AB82DFE12450048008B /* Debug */,
				8EBD9AB92DFE12450048008B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8EBD9ABA2DFE12450048008B /* Build configuration list for PBXNativeTarget "Pimstats" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EBD9ABB2DFE12450048008B /* Debug */,
				8EBD9ABC2DFE12450048008B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8EBD9ABD2DFE12450048008B /* Build configuration list for PBXNativeTarget "PimstatsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EBD9ABE2DFE12450048008B /* Debug */,
				8EBD9ABF2DFE12450048008B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8EBD9AC02DFE12450048008B /* Build configuration list for PBXNativeTarget "PimstatsUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8EBD9AC12DFE12450048008B /* Debug */,
				8EBD9AC22DFE12450048008B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8EBD9A8F2DFE12430048008B /* Project object */;
}
