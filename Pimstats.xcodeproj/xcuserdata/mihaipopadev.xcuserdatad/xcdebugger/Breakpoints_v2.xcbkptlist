<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "585667AF-79C1-4D25-8238-C8AB078A5FA7"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D0B55395-7F6D-46C5-82D6-175DFD778875"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Pimstats/BarRacerView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "162"
            endingLineNumber = "162"
            landmarkName = "getTeamColor(_:dataManager:)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4A106966-3222-4EA8-881F-A356DFA05D5D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Pimstats/BarRacerView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "160"
            endingLineNumber = "160"
            landmarkName = "getTeamColor(_:dataManager:)"
            landmarkType = "9">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
